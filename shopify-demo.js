import 'dotenv/config';
import Shopify from 'shopify-api-node';

const accessToken = process.env.SHOPIFY_API_KEY;
const shopName = process.env.SHOPIFY_SHOP_NAME;
const shopify = new Shopify({ shopName, accessToken });

// Helper function to delay execution
const delay = (ms) => new Promise(resolve => setTimeout(resolve, ms));

// Sample product data with variants, vendors, product types, and tags
const productsData = [
    {
        title: "Premium Wireless Headphones",
        body_html: "<p>High-quality wireless headphones with noise cancellation.</p>",
        vendor: "AudioTech",
        product_type: "Electronics",
        tags: ["electronics", "audio", "wireless", "premium"],
        variants: [
            { title: "Black", price: "199.99", inventory_quantity: 25, sku: "WH-001-BLK" },
            { title: "White", price: "199.99", inventory_quantity: 15, sku: "WH-001-WHT" },
            { title: "Silver", price: "199.99", inventory_quantity: 0, sku: "WH-001-SLV" }
        ],
        images: [{ src: "https://via.placeholder.com/800x600/000000/FFFFFF?text=Headphones" }]
    },
    {
        title: "Organic Cotton T-Shirt",
        body_html: "<p>Comfortable organic cotton t-shirt made from sustainable materials.</p>",
        vendor: "EcoWear",
        product_type: "Clothing",
        tags: ["clothing", "organic", "cotton", "sustainable"],
        variants: [
            { title: "Small / Blue", price: "29.99", inventory_quantity: 50, sku: "TS-001-S-BLU" },
            { title: "Medium / Blue", price: "29.99", inventory_quantity: 40, sku: "TS-001-M-BLU" },
            { title: "Large / Blue", price: "29.99", inventory_quantity: 30, sku: "TS-001-L-BLU" },
            { title: "Small / Red", price: "29.99", inventory_quantity: 0, sku: "TS-001-S-RED" }
        ],
        images: [{ src: "https://via.placeholder.com/800x600/0066CC/FFFFFF?text=T-Shirt" }]
    },
    {
        title: "Smart Fitness Watch",
        body_html: "<p>Advanced fitness tracking watch with heart rate monitoring.</p>",
        vendor: "FitTech",
        product_type: "Wearables",
        tags: ["fitness", "smartwatch", "health", "technology"],
        variants: [
            { title: "42mm Black", price: "299.99", inventory_quantity: 20, sku: "SW-001-42-BLK" },
            { title: "44mm Black", price: "329.99", inventory_quantity: 15, sku: "SW-001-44-BLK" }
        ],
        images: [{ src: "https://via.placeholder.com/800x600/000000/FFFFFF?text=Smart+Watch" }]
    },
    {
        title: "Artisan Coffee Beans",
        body_html: "<p>Premium single-origin coffee beans roasted to perfection.</p>",
        vendor: "Mountain Roasters",
        product_type: "Food & Beverage",
        tags: ["coffee", "organic", "fair-trade", "artisan"],
        variants: [
            { title: "Light Roast 250g", price: "18.99", inventory_quantity: 100, sku: "CF-001-L-250" },
            { title: "Medium Roast 250g", price: "18.99", inventory_quantity: 85, sku: "CF-001-M-250" },
            { title: "Dark Roast 250g", price: "18.99", inventory_quantity: 0, sku: "CF-001-D-250" }
        ],
        images: [{ src: "https://via.placeholder.com/800x600/8B4513/FFFFFF?text=Coffee+Beans" }]
    },
    {
        title: "Yoga Mat Premium",
        body_html: "<p>Non-slip premium yoga mat perfect for all types of yoga practice.</p>",
        vendor: "ZenFit",
        product_type: "Sports & Recreation",
        tags: ["yoga", "fitness", "exercise", "wellness"],
        variants: [
            { title: "Purple 6mm", price: "49.99", inventory_quantity: 30, sku: "YM-001-PUR-6" },
            { title: "Blue 6mm", price: "49.99", inventory_quantity: 25, sku: "YM-001-BLU-6" }
        ],
        images: [{ src: "https://via.placeholder.com/800x600/9932CC/FFFFFF?text=Yoga+Mat" }]
    }
];

// Function to create a single product
async function createProduct(productData) {
    try {
        console.log(`Creating product: ${productData.title}`);
        
        const product = await shopify.product.create({
            title: productData.title,
            body_html: productData.body_html,
            vendor: productData.vendor,
            product_type: productData.product_type,
            tags: productData.tags.join(', '),
            variants: productData.variants.map(variant => ({
                title: variant.title,
                price: variant.price,
                inventory_quantity: variant.inventory_quantity,
                sku: variant.sku,
                inventory_management: 'shopify',
                inventory_policy: 'deny'
            })),
            images: productData.images
        });
        
        console.log(`✅ Created: ${product.title} (ID: ${product.id})`);
        return product;
    } catch (error) {
        console.error(`❌ Error creating ${productData.title}:`, error.message);
        return null;
    }
}

// Function to create additional simple products
async function createSimpleProducts() {
    const simpleProducts = [];
    
    for (let i = 6; i <= 15; i++) {
        const productData = {
            title: `Product ${i}`,
            body_html: `<p>Description for product ${i}.</p>`,
            vendor: `Vendor${i}`,
            product_type: "General",
            tags: ["general", `product${i}`],
            variants: [{ 
                title: "Default", 
                price: `${(i * 10 + 9)}.99`, 
                inventory_quantity: i % 2 === 0 ? 0 : 25, 
                sku: `PROD-${i.toString().padStart(3, '0')}` 
            }],
            images: [{ src: `https://via.placeholder.com/800x600/FF${(i * 20).toString(16).padStart(4, '0')}/FFFFFF?text=Product+${i}` }]
        };
        
        const product = await createProduct(productData);
        if (product) simpleProducts.push(product);
        await delay(1000);
    }
    
    return simpleProducts;
}

// Function to add "bulk" tag to all products
async function addBulkTagToProducts(products) {
    console.log('\n🏷️  Adding "bulk" tag to all products...\n');
    
    for (const product of products) {
        try {
            const existingTags = product.tags ? product.tags.split(', ') : [];
            if (!existingTags.includes('bulk')) {
                existingTags.push('bulk');
                
                await shopify.product.update(product.id, {
                    tags: existingTags.join(', ')
                });
                
                console.log(`✅ Added "bulk" tag to: ${product.title}`);
            }
            await delay(500);
        } catch (error) {
            console.error(`❌ Error adding bulk tag to ${product.title}:`, error.message);
        }
    }
}

// Function to create custom collection (manual)
async function createCustomCollection(title, description, productIds = []) {
    try {
        console.log(`Creating custom collection: ${title}`);
        
        const collection = await shopify.customCollection.create({
            title: title,
            body_html: description,
            sort_order: 'alpha-asc',
            published: true
        });
        
        // Add products to the collection
        for (const productId of productIds) {
            await shopify.collect.create({
                collection_id: collection.id,
                product_id: productId
            });
            await delay(300);
        }
        
        console.log(`✅ Created custom collection: ${collection.title}`);
        return collection;
    } catch (error) {
        console.error(`❌ Error creating custom collection:`, error.message);
        return null;
    }
}

// Function to create smart collection (automated)
async function createSmartCollection(title, description, rules, sortOrder = 'alpha-asc') {
    try {
        console.log(`Creating smart collection: ${title}`);
        
        const collection = await shopify.smartCollection.create({
            title: title,
            body_html: description,
            rules: rules,
            sort_order: sortOrder,
            published: true,
            disjunctive: false
        });
        
        console.log(`✅ Created smart collection: ${collection.title}`);
        return collection;
    } catch (error) {
        console.error(`❌ Error creating smart collection:`, error.message);
        return null;
    }
}

// Main execution function
async function main() {
    try {
        console.log('🎯 Shopify Product & Collection Demo');
        console.log('====================================\n');
        
        // Get shop info
        const shop = await shopify.shop.get();
        console.log(`Store: ${shop.name} (${shop.myshopify_domain})\n`);
        
        // Step 1: Create 5 detailed products
        console.log('📦 Creating 5 detailed products with variants...\n');
        const detailedProducts = [];
        for (const productData of productsData) {
            const product = await createProduct(productData);
            if (product) detailedProducts.push(product);
            await delay(1000);
        }
        
        // Step 2: Create 10 simple products
        console.log('\n📦 Creating 10 additional simple products...\n');
        const simpleProducts = await createSimpleProducts();
        
        const allProducts = [...detailedProducts, ...simpleProducts];
        console.log(`\n✅ Created ${allProducts.length} total products\n`);
        
        // Step 3: Add "bulk" tag to all products
        await addBulkTagToProducts(allProducts);
        
        // Step 4: Create collections
        console.log('\n📁 Creating collections...\n');
        
        // Custom Collection (Manual)
        const electronicsProducts = allProducts.filter(p => 
            p.product_type === 'Electronics'
        );
        const customCollection = await createCustomCollection(
            'Electronics Collection',
            '<p>Curated collection of electronic devices.</p>',
            electronicsProducts.slice(0, 3).map(p => p.id)
        );
        
        await delay(1000);
        
        // Smart Collection 1 - Premium products
        const smartCollection1 = await createSmartCollection(
            'Premium Products',
            '<p>Automatically curated premium products.</p>',
            [{ column: 'tag', relation: 'equals', condition: 'premium' }],
            'price-desc'
        );
        
        await delay(1000);
        
        // Smart Collection 2 - Affordable products
        const smartCollection2 = await createSmartCollection(
            'Affordable Finds',
            '<p>Great products under $50.</p>',
            [{ column: 'variant_price', relation: 'less_than', condition: '50.00' }],
            'price-asc'
        );
        
        // Summary
        console.log('\n🎉 DEMO COMPLETE!');
        console.log('=================');
        console.log(`✅ Created ${allProducts.length} products`);
        console.log(`✅ Added "bulk" tag to all products`);
        console.log(`✅ Created 1 Custom Collection (Manual)`);
        console.log(`✅ Created 2 Smart Collections (Automated)`);
        console.log(`✅ Collections sorted by A-Z order`);
        console.log('\n💡 Note: To change catalog sorting to lowest prices first,');
        console.log('   modify your theme or use Shopify Admin interface.');
        
    } catch (error) {
        console.error('❌ Error in main execution:', error.message);
    }
}

// Execute the demo
main().catch(console.error);
