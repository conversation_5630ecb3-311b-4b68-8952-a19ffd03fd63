{"name": "stopcock", "version": "1.1.0", "description": "Limit the execution rate of a function using the token bucket algorithm", "homepage": "https://github.com/lpinca/stopcock", "bugs": "https://github.com/lpinca/stopcock/issues", "repository": "lpinca/stopcock", "author": "<PERSON>", "license": "MIT", "main": "index.js", "keywords": ["token bucket", "rate-limit", "function", "throttle", "promise"], "scripts": {"coverage": "npm test && nyc report --reporter=html", "test": "nyc tape test.js"}, "engines": {"node": ">=4.0.0"}, "files": ["index.js"], "devDependencies": {"coveralls": "^3.0.3", "nyc": "^14.0.0", "tape": "^4.10.1"}}