import 'dotenv/config';
import Shopify from 'shopify-api-node';

const accessToken = process.env.SHOPIFY_API_KEY;
const shopName = process.env.SHOPIFY_SHOP_NAME;
const shopify = new Shopify({ shopName, accessToken });

(async () => {
    const shopInfo = await shopify.shop.get();
    console.log(`Store created: ${shopInfo.name}`);
    const products = await shopify.product.list();
    for (const product of products) {
        await shopify.product.delete(product.id);
    }
    // for (let i = 1; i <= 15; i++) {
    //     await shopify.product.create({
    //         title: `Product ${i}`,
    //         body_html: `Description for Product ${i}`,
    //         variants: [{ price: (20 + i * 2).toFixed(2) }]
    //     });
    // }

})()