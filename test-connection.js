import 'dotenv/config';
import Shopify from 'shopify-api-node';

const accessToken = process.env.SHOPIFY_API_KEY;
const shopName = process.env.SHOPIFY_SHOP_NAME;

console.log('Testing Shopify connection...');
console.log('Shop Name:', shopName);
console.log('Access Token:', accessToken ? 'Present' : 'Missing');

const shopify = new Shopify({ shopName, accessToken });

// Test connection
shopify.shop.get()
  .then(shop => {
    console.log('\n✅ Connection successful!');
    console.log(`Store: ${shop.name}`);
    console.log(`Domain: ${shop.myshopify_domain}`);
    console.log(`Email: ${shop.email}`);
    console.log(`Currency: ${shop.currency}`);
  })
  .catch(error => {
    console.error('\n❌ Connection failed:', error.message);
  });
