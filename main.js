import 'dotenv/config';
import Shopify from 'shopify-api-node';

const accessToken = process.env.SHOPIFY_API_KEY;
const shopName = process.env.SHOPIFY_SHOP_NAME;

// Sample product data
const productsData = [
    {
        title: "Premium Wireless Headphones",
        body_html: "<p>High-quality wireless headphones with noise cancellation and premium sound quality.</p>",
        vendor: "AudioTech",
        product_type: "Electronics",
        tags: ["electronics", "audio", "wireless", "premium"],
        variants: [
            { title: "Black", price: "199.99", inventory_quantity: 25, sku: "WH-001-BLK" },
            { title: "White", price: "199.99", inventory_quantity: 15, sku: "WH-001-WHT" },
            { title: "Silver", price: "199.99", inventory_quantity: 0, sku: "WH-001-SLV" }
        ],
        images: [
            { src: "https://via.placeholder.com/800x600/000000/FFFFFF?text=Headphones+Main" },
            { src: "https://via.placeholder.com/800x600/333333/FFFFFF?text=Headphones+Side" }
        ]
    },
    {
        title: "Organic Cotton T-Shirt",
        body_html: "<p>Comfortable organic cotton t-shirt made from sustainable materials.</p>",
        vendor: "EcoWear",
        product_type: "Clothing",
        tags: ["clothing", "organic", "cotton", "sustainable"],
        variants: [
            { title: "Small / Blue", price: "29.99", inventory_quantity: 50, sku: "TS-001-S-BLU", option1: "Small", option2: "Blue" },
            { title: "Medium / Blue", price: "29.99", inventory_quantity: 40, sku: "TS-001-M-BLU", option1: "Medium", option2: "Blue" },
            { title: "Large / Blue", price: "29.99", inventory_quantity: 30, sku: "TS-001-L-BLU", option1: "Large", option2: "Blue" },
            { title: "Small / Red", price: "29.99", inventory_quantity: 0, sku: "TS-001-S-RED", option1: "Small", option2: "Red" },
            { title: "Medium / Red", price: "29.99", inventory_quantity: 25, sku: "TS-001-M-RED", option1: "Medium", option2: "Red" }
        ],
        options: [
            { name: "Size", values: ["Small", "Medium", "Large"] },
            { name: "Color", values: ["Blue", "Red"] }
        ],
        images: [
            { src: "https://via.placeholder.com/800x600/0066CC/FFFFFF?text=T-Shirt+Blue" },
            { src: "https://via.placeholder.com/800x600/CC0000/FFFFFF?text=T-Shirt+Red" }
        ]
    },
    {
        title: "Smart Fitness Watch",
        body_html: "<p>Advanced fitness tracking watch with heart rate monitoring and GPS.</p>",
        vendor: "FitTech",
        product_type: "Wearables",
        tags: ["fitness", "smartwatch", "health", "technology"],
        variants: [
            { title: "42mm Black", price: "299.99", inventory_quantity: 20, sku: "SW-001-42-BLK" },
            { title: "44mm Black", price: "329.99", inventory_quantity: 15, sku: "SW-001-44-BLK" },
            { title: "42mm Rose Gold", price: "349.99", inventory_quantity: 8, sku: "SW-001-42-RG" }
        ],
        images: [
            { src: "https://via.placeholder.com/800x600/000000/FFFFFF?text=Smart+Watch" }
        ]
    },
    {
        title: "Artisan Coffee Beans",
        body_html: "<p>Premium single-origin coffee beans roasted to perfection.</p>",
        vendor: "Mountain Roasters",
        product_type: "Food & Beverage",
        tags: ["coffee", "organic", "fair-trade", "artisan"],
        variants: [
            { title: "Light Roast 250g", price: "18.99", inventory_quantity: 100, sku: "CF-001-L-250" },
            { title: "Medium Roast 250g", price: "18.99", inventory_quantity: 85, sku: "CF-001-M-250" },
            { title: "Dark Roast 250g", price: "18.99", inventory_quantity: 0, sku: "CF-001-D-250" },
            { title: "Light Roast 500g", price: "34.99", inventory_quantity: 50, sku: "CF-001-L-500" }
        ],
        images: [
            { src: "https://via.placeholder.com/800x600/8B4513/FFFFFF?text=Coffee+Beans" }
        ]
    },
    {
        title: "Yoga Mat Premium",
        body_html: "<p>Non-slip premium yoga mat perfect for all types of yoga practice.</p>",
        vendor: "ZenFit",
        product_type: "Sports & Recreation",
        tags: ["yoga", "fitness", "exercise", "wellness"],
        variants: [
            { title: "Purple 6mm", price: "49.99", inventory_quantity: 30, sku: "YM-001-PUR-6" },
            { title: "Blue 6mm", price: "49.99", inventory_quantity: 25, sku: "YM-001-BLU-6" },
            { title: "Green 8mm", price: "59.99", inventory_quantity: 20, sku: "YM-001-GRN-8" }
        ],
        images: [
            { src: "https://via.placeholder.com/800x600/9932CC/FFFFFF?text=Yoga+Mat" }
        ]
    }
];

// Additional products (6-15) with simpler configurations
const additionalProducts = [
    {
        title: "Ceramic Coffee Mug",
        body_html: "<p>Beautiful handcrafted ceramic coffee mug.</p>",
        vendor: "Pottery Works",
        product_type: "Home & Kitchen",
        tags: ["kitchen", "ceramic", "handmade"],
        variants: [{ title: "Default", price: "24.99", inventory_quantity: 75, sku: "MUG-001" }],
        images: [{ src: "https://via.placeholder.com/800x600/8B4513/FFFFFF?text=Coffee+Mug" }]
    },
    {
        title: "Bluetooth Speaker",
        body_html: "<p>Portable Bluetooth speaker with excellent sound quality.</p>",
        vendor: "SoundWave",
        product_type: "Electronics",
        tags: ["electronics", "audio", "portable"],
        variants: [{ title: "Default", price: "79.99", inventory_quantity: 0, sku: "SPK-001" }],
        images: [{ src: "https://via.placeholder.com/800x600/000000/FFFFFF?text=Speaker" }]
    },
    {
        title: "Reading Glasses",
        body_html: "<p>Stylish reading glasses for comfortable reading.</p>",
        vendor: "VisionCare",
        product_type: "Accessories",
        tags: ["glasses", "reading", "accessories"],
        variants: [
            { title: "****", price: "39.99", inventory_quantity: 20, sku: "RG-001-1" },
            { title: "****", price: "39.99", inventory_quantity: 15, sku: "RG-001-15" },
            { title: "****", price: "39.99", inventory_quantity: 10, sku: "RG-001-2" }
        ],
        images: [{ src: "https://via.placeholder.com/800x600/333333/FFFFFF?text=Reading+Glasses" }]
    },
    {
        title: "Desk Lamp LED",
        body_html: "<p>Modern LED desk lamp with adjustable brightness.</p>",
        vendor: "LightCraft",
        product_type: "Home & Office",
        tags: ["lighting", "led", "office", "modern"],
        variants: [{ title: "Default", price: "89.99", inventory_quantity: 35, sku: "DL-001" }],
        images: [{ src: "https://via.placeholder.com/800x600/FFFF00/000000?text=Desk+Lamp" }]
    },
    {
        title: "Notebook Set",
        body_html: "<p>Set of 3 premium notebooks for writing and sketching.</p>",
        vendor: "PaperCraft",
        product_type: "Stationery",
        tags: ["notebook", "writing", "stationery"],
        variants: [{ title: "Set of 3", price: "19.99", inventory_quantity: 60, sku: "NB-001-SET" }],
        images: [{ src: "https://via.placeholder.com/800x600/FF6347/FFFFFF?text=Notebook+Set" }]
    }
];

// Combine all products
const allProducts = [...productsData, ...additionalProducts];

// Add 5 more simple products to reach 15 total
for (let i = 11; i <= 15; i++) {
    allProducts.push({
        title: `Product ${i}`,
        body_html: `<p>Description for product ${i}.</p>`,
        vendor: `Vendor${i}`,
        product_type: "General",
        tags: ["general", `product${i}`],
        variants: [{ title: "Default", price: `${(i * 10 + 9)}.99`, inventory_quantity: i % 2 === 0 ? 0 : 25, sku: `PROD-${i.toString().padStart(3, '0')}` }],
        images: [{ src: `https://via.placeholder.com/800x600/FF${(i * 20).toString(16).padStart(4, '0')}/FFFFFF?text=Product+${i}` }]
    });
}

// Initialize Shopify client
const shopify = new Shopify({ shopName, accessToken });

// Helper function to delay execution
const delay = (ms) => new Promise(resolve => setTimeout(resolve, ms));

// Function to create a single product
async function createProduct(productData) {
    try {
        console.log(`Creating product: ${productData.title}`);

        const product = await shopify.product.create({
            title: productData.title,
            body_html: productData.body_html,
            vendor: productData.vendor,
            product_type: productData.product_type,
            tags: productData.tags.join(', '),
            options: productData.options || [{ name: "Title", values: ["Default Title"] }],
            variants: productData.variants.map(variant => ({
                title: variant.title,
                price: variant.price,
                inventory_quantity: variant.inventory_quantity,
                sku: variant.sku,
                option1: variant.option1 || variant.title,
                option2: variant.option2 || null,
                inventory_management: 'shopify',
                inventory_policy: 'deny'
            })),
            images: productData.images
        });

        console.log(`✅ Created product: ${product.title} (ID: ${product.id})`);
        return product;
    } catch (error) {
        console.error(`❌ Error creating product ${productData.title}:`, error.message);
        return null;
    }
}

// Function to create all products
async function createAllProducts() {
    console.log('\n🚀 Starting to create 15 products...\n');
    const createdProducts = [];

    for (let i = 0; i < allProducts.length; i++) {
        const product = await createProduct(allProducts[i]);
        if (product) {
            createdProducts.push(product);
        }

        // Add delay to avoid rate limiting
        if (i < allProducts.length - 1) {
            await delay(1000); // 1 second delay between requests
        }
    }

    console.log(`\n✅ Successfully created ${createdProducts.length} products\n`);
    return createdProducts;
}

// Function to add "bulk" tag to all products
async function addBulkTagToProducts(products) {
    console.log('\n🏷️  Adding "bulk" tag to all products...\n');

    for (const product of products) {
        try {
            const existingTags = product.tags ? product.tags.split(', ') : [];
            if (!existingTags.includes('bulk')) {
                existingTags.push('bulk');

                await shopify.product.update(product.id, {
                    tags: existingTags.join(', ')
                });

                console.log(`✅ Added "bulk" tag to: ${product.title}`);
            }
            await delay(500); // Small delay to avoid rate limiting
        } catch (error) {
            console.error(`❌ Error adding bulk tag to ${product.title}:`, error.message);
        }
    }

    console.log('\n✅ Finished adding "bulk" tags\n');
}

// Function to create a custom collection (manual)
async function createCustomCollection(title, description, productIds = []) {
    try {
        console.log(`Creating custom collection: ${title}`);

        const collection = await shopify.customCollection.create({
            title: title,
            body_html: description,
            sort_order: 'alpha-asc',
            published: true
        });

        // Add products to the collection if provided
        if (productIds.length > 0) {
            for (const productId of productIds) {
                await shopify.collect.create({
                    collection_id: collection.id,
                    product_id: productId
                });
                await delay(300);
            }
        }

        console.log(`✅ Created custom collection: ${collection.title} (ID: ${collection.id})`);
        return collection;
    } catch (error) {
        console.error(`❌ Error creating custom collection ${title}:`, error.message);
        return null;
    }
}

// Function to create a smart collection (automated)
async function createSmartCollection(title, description, rules, sortOrder = 'alpha-asc') {
    try {
        console.log(`Creating smart collection: ${title}`);

        const collection = await shopify.smartCollection.create({
            title: title,
            body_html: description,
            rules: rules,
            sort_order: sortOrder,
            published: true,
            disjunctive: false // AND logic for rules
        });

        console.log(`✅ Created smart collection: ${collection.title} (ID: ${collection.id})`);
        return collection;
    } catch (error) {
        console.error(`❌ Error creating smart collection ${title}:`, error.message);
        return null;
    }
}

// Function to create all collections
async function createAllCollections(products) {
    console.log('\n📁 Creating collections...\n');

    const collections = [];

    // 1. Create Custom Collection (Manual)
    const electronicsProducts = products.filter(p =>
        p.product_type === 'Electronics' || p.tags.includes('electronics')
    );
    const customCollection = await createCustomCollection(
        'Electronics Collection',
        '<p>Curated collection of electronic devices and gadgets.</p>',
        electronicsProducts.slice(0, 3).map(p => p.id)
    );
    if (customCollection) collections.push(customCollection);

    await delay(1000);

    // 2. Create Smart Collection - Products with "premium" tag
    const premiumSmartCollection = await createSmartCollection(
        'Premium Products',
        '<p>Automatically curated premium products.</p>',
        [
            {
                column: 'tag',
                relation: 'equals',
                condition: 'premium'
            }
        ],
        'price-desc' // Sort by price descending
    );
    if (premiumSmartCollection) collections.push(premiumSmartCollection);

    await delay(1000);

    // 3. Create Smart Collection - Products under $50
    const affordableSmartCollection = await createSmartCollection(
        'Affordable Finds',
        '<p>Great products under $50.</p>',
        [
            {
                column: 'variant_price',
                relation: 'less_than',
                condition: '50.00'
            }
        ],
        'price-asc' // Sort by price ascending (lowest first)
    );
    if (affordableSmartCollection) collections.push(affordableSmartCollection);

    console.log(`\n✅ Successfully created ${collections.length} collections\n`);
    return collections;
}

// Function to update collection sorting
async function updateCollectionSorting(collections) {
    console.log('\n🔄 Updating collection sorting to A-Z...\n');

    for (const collection of collections) {
        try {
            if (collection.id) {
                // Update custom collections
                if (collection.handle) {
                    await shopify.customCollection.update(collection.id, {
                        sort_order: 'alpha-asc'
                    });
                } else {
                    // Update smart collections
                    await shopify.smartCollection.update(collection.id, {
                        sort_order: 'alpha-asc'
                    });
                }
                console.log(`✅ Updated sorting for: ${collection.title}`);
                await delay(500);
            }
        } catch (error) {
            console.error(`❌ Error updating sorting for ${collection.title}:`, error.message);
        }
    }

    console.log('\n✅ Finished updating collection sorting\n');
}

// Function to display shop information and catalog settings
async function displayShopInfo() {
    try {
        console.log('\n🏪 Shop Information:\n');
        const shop = await shopify.shop.get();
        console.log(`Store Name: ${shop.name}`);
        console.log(`Store URL: ${shop.myshopify_domain}`);
        console.log(`Store Email: ${shop.email}`);
        console.log(`Currency: ${shop.currency}`);
        console.log(`Timezone: ${shop.timezone}`);

        // Note: Catalog sorting is typically handled in the theme/frontend
        console.log('\n📝 Note: To change catalog page sorting to display lowest prices first,');
        console.log('you would need to modify your theme\'s collection template or use');
        console.log('the Shopify Admin interface to set default collection sorting.');

    } catch (error) {
        console.error('❌ Error getting shop info:', error.message);
    }
}

// Main execution function
async function main() {
    try {
        console.log('🎯 Shopify Product & Collection Management Script');
        console.log('================================================\n');

        // Display shop information
        await displayShopInfo();

        // Step 1: Create 15 products
        const createdProducts = await createAllProducts();

        if (createdProducts.length === 0) {
            console.log('❌ No products were created. Exiting...');
            return;
        }

        // Step 2: Add "bulk" tag to all created products
        await addBulkTagToProducts(createdProducts);

        // Step 3: Create collections (1 manual, 2 automated)
        const createdCollections = await createAllCollections(createdProducts);

        // Step 4: Update collection sorting to A-Z
        await updateCollectionSorting(createdCollections);

        // Summary
        console.log('\n🎉 SUMMARY');
        console.log('==========');
        console.log(`✅ Created ${createdProducts.length} products`);
        console.log(`✅ Added "bulk" tag to all products`);
        console.log(`✅ Created ${createdCollections.length} collections`);
        console.log(`✅ Updated collection sorting to A-Z`);
        console.log('\n📋 What was accomplished:');
        console.log('• 15 new products with variants, vendors, product types, and tags');
        console.log('• Inventory adjusted (some in-stock, some out-of-stock)');
        console.log('• Thumbnail and variant images added');
        console.log('• "bulk" tag added to all products using bulk operations');
        console.log('• 1 Custom Collection (Manual) created');
        console.log('• 2 Smart Collections (Automated) created');
        console.log('• Collections sorted by A-Z order');
        console.log('\n💡 Additional Notes:');
        console.log('• To change catalog page sorting to lowest prices first,');
        console.log('  modify your theme\'s collection template or use Shopify Admin');
        console.log('• All products include proper variants with different inventory levels');
        console.log('• Images are placeholder URLs - replace with actual product images');

    } catch (error) {
        console.error('❌ Fatal error in main execution:', error.message);
        console.error(error.stack);
    }
}

// Execute the main function
main().catch(console.error);