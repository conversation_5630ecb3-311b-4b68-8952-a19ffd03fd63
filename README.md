# Shopify Product & Collection Management Script

This Node.js script creates products and collections in a Shopify store using the Shopify Admin API.

## Features

### Products (15 total)
- ✅ Creates 15 new products with detailed information
- ✅ Includes variants, vendors, product types, and tags
- ✅ Adjusts inventory levels (some in-stock, some out-of-stock)
- ✅ Adds thumbnail and variant images (placeholder URLs)
- ✅ Uses bulk operations to add "bulk" tag to all products

### Collections (3 total)
- ✅ 1 Custom Collection (Manual): "Electronics Collection"
- ✅ 2 Smart Collections (Automated):
  - "Premium Products" (products with "premium" tag)
  - "Affordable Finds" (products under $50)
- ✅ Sorts collections by A-Z order

### Sample Products Created

1. **Premium Wireless Headphones** - AudioTech (Electronics)
   - Variants: Black, White, Silver
   - Price: $199.99
   - Tags: electronics, audio, wireless, premium

2. **Organic Cotton T-Shirt** - EcoWear (Clothing)
   - Variants: Small/Medium/Large in Blue/Red
   - Price: $29.99
   - Tags: clothing, organic, cotton, sustainable

3. **Smart Fitness Watch** - FitTech (Wearables)
   - Variants: 42mm/44mm Black, 42mm Rose Gold
   - Price: $299.99-$349.99
   - Tags: fitness, smartwatch, health, technology

4. **Artisan Coffee Beans** - Mountain Roasters (Food & Beverage)
   - Variants: Light/Medium/Dark roast in 250g/500g
   - Price: $18.99-$34.99
   - Tags: coffee, organic, fair-trade, artisan

5. **Yoga Mat Premium** - ZenFit (Sports & Recreation)
   - Variants: Purple/Blue 6mm, Green 8mm
   - Price: $49.99-$59.99
   - Tags: yoga, fitness, exercise, wellness

...and 10 more products with various configurations.

## Prerequisites

- Node.js installed
- Shopify store with Admin API access
- Valid Shopify API credentials

## Setup

1. Install dependencies:
   ```bash
   npm install
   ```

2. Configure environment variables in `.env`:
   ```
   SHOPIFY_API_KEY=your_admin_api_access_token
   SHOPIFY_SHOP_NAME=your-shop-name
   ```

## Usage

Run the script:
```bash
node index.js
```

The script will:
1. Display shop information
2. Create 15 products with variants and images
3. Add "bulk" tag to all created products
4. Create 3 collections (1 manual, 2 automated)
5. Sort collections by A-Z order
6. Display a summary of all actions

## Important Notes

### Catalog Sorting
To change the catalog page sorting to display lowest prices first:
- **Option 1**: Use Shopify Admin → Collections → [Collection Name] → Sort → Price (low to high)
- **Option 2**: Modify your theme's collection template to use `sort_by: 'price-ascending'`
- **Option 3**: Use collection URL parameters: `?sort_by=price-ascending`

### Images
The script uses placeholder images from `via.placeholder.com`. Replace these URLs with actual product images for production use.

### Rate Limiting
The script includes delays between API calls to respect Shopify's rate limits.

## API Endpoints Used

- `shopify.product.create()` - Create products
- `shopify.product.update()` - Add bulk tags
- `shopify.customCollection.create()` - Create manual collections
- `shopify.smartCollection.create()` - Create automated collections
- `shopify.collect.create()` - Add products to collections
- `shopify.shop.get()` - Get shop information

## Error Handling

The script includes comprehensive error handling and will continue execution even if individual operations fail.

## Output

The script provides detailed console output showing:
- Progress of product creation
- Collection creation status
- Summary of all completed actions
- Any errors encountered

## Customization

You can modify the `productsData` and `additionalProducts` arrays in `index.js` to create different products with your own specifications.
