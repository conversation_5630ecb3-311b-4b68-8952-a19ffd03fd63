import 'dotenv/config';
import Shopify from 'shopify-api-node';

const accessToken = process.env.SHOPIFY_API_KEY;
const shopName = process.env.SHOPIFY_SHOP_NAME;
const shopify = new Shopify({ shopName, accessToken });

const delay = (ms) => new Promise(resolve => setTimeout(resolve, ms));

async function addVariantsToExistingProducts() {
    console.log('🔧 Adding variants to existing products...\n');
    
    try {
        // Get existing products
        const products = await shopify.product.list({ limit: 10 });
        console.log(`Found ${products.length} existing products\n`);
        
        // Add variants to first 5 products
        for (let i = 0; i < Math.min(5, products.length); i++) {
            const product = products[i];
            console.log(`Adding variants to: ${product.title}`);
            
            try {
                // Add a variant with different properties
                const variant1 = await shopify.productVariant.create(product.id, {
                    title: `${product.title} - Variant A`,
                    price: (parseFloat(product.variants[0].price) + 10).toFixed(2),
                    inventory_quantity: 15,
                    sku: `${product.variants[0].sku}-VAR-A`,
                    inventory_management: 'shopify',
                    inventory_policy: 'deny'
                });
                
                console.log(`  ✅ Added variant: ${variant1.title}`);
                await delay(500);
                
                // Add another variant (some out of stock)
                const variant2 = await shopify.productVariant.create(product.id, {
                    title: `${product.title} - Variant B`,
                    price: (parseFloat(product.variants[0].price) + 20).toFixed(2),
                    inventory_quantity: i % 2 === 0 ? 0 : 25, // Some out of stock
                    sku: `${product.variants[0].sku}-VAR-B`,
                    inventory_management: 'shopify',
                    inventory_policy: 'deny'
                });
                
                console.log(`  ✅ Added variant: ${variant2.title} ${variant2.inventory_quantity === 0 ? '(OUT OF STOCK)' : ''}`);
                await delay(500);
                
                // Update product with additional vendor and product type info
                await shopify.product.update(product.id, {
                    vendor: `Vendor-${i + 1}`,
                    product_type: ['Electronics', 'Clothing', 'Home & Garden', 'Sports', 'Books'][i],
                    tags: `${product.tags || ''}, variant-product, updated`.replace(/^, /, '')
                });
                
                console.log(`  ✅ Updated vendor and product type\n`);
                await delay(1000);
                
            } catch (error) {
                console.error(`  ❌ Error adding variants to ${product.title}:`, error.message);
            }
        }
        
        console.log('✅ Finished adding variants to products\n');
        
    } catch (error) {
        console.error('❌ Error getting products:', error.message);
    }
}

async function addImagesToProducts() {
    console.log('🖼️  Adding images to products...\n');
    
    try {
        const products = await shopify.product.list({ limit: 5 });
        
        for (let i = 0; i < products.length; i++) {
            const product = products[i];
            
            try {
                // Add a product image
                const image = await shopify.productImage.create(product.id, {
                    src: `https://via.placeholder.com/800x600/FF${(i * 30 + 100).toString(16).padStart(4, '0')}/FFFFFF?text=${encodeURIComponent(product.title)}`,
                    alt: `${product.title} - Main Image`
                });
                
                console.log(`✅ Added image to: ${product.title}`);
                await delay(500);
                
            } catch (error) {
                console.error(`❌ Error adding image to ${product.title}:`, error.message);
            }
        }
        
    } catch (error) {
        console.error('❌ Error adding images:', error.message);
    }
}

async function demonstrateCollectionSorting() {
    console.log('\n📊 Demonstrating collection sorting...\n');
    
    try {
        // Get existing collections
        const customCollections = await shopify.customCollection.list();
        const smartCollections = await shopify.smartCollection.list();
        
        console.log(`Found ${customCollections.length} custom collections and ${smartCollections.length} smart collections\n`);
        
        // Update custom collections to A-Z sorting
        for (const collection of customCollections) {
            try {
                await shopify.customCollection.update(collection.id, {
                    sort_order: 'alpha-asc'
                });
                console.log(`✅ Updated custom collection "${collection.title}" to A-Z sorting`);
                await delay(500);
            } catch (error) {
                console.error(`❌ Error updating ${collection.title}:`, error.message);
            }
        }
        
        // Update smart collections to A-Z sorting
        for (const collection of smartCollections) {
            try {
                await shopify.smartCollection.update(collection.id, {
                    sort_order: 'alpha-asc'
                });
                console.log(`✅ Updated smart collection "${collection.title}" to A-Z sorting`);
                await delay(500);
            } catch (error) {
                console.error(`❌ Error updating ${collection.title}:`, error.message);
            }
        }
        
        // Create a new collection sorted by lowest price first
        try {
            const priceCollection = await shopify.smartCollection.create({
                title: 'Lowest Prices First',
                body_html: '<p>Products sorted by lowest price first for catalog demonstration.</p>',
                rules: [{
                    column: 'variant_price',
                    relation: 'greater_than',
                    condition: '0.00'
                }],
                sort_order: 'price-asc', // Lowest prices first
                published: true,
                disjunctive: false
            });
            
            console.log(`✅ Created collection "${priceCollection.title}" with lowest prices first sorting`);
        } catch (error) {
            console.error('❌ Error creating price-sorted collection:', error.message);
        }
        
    } catch (error) {
        console.error('❌ Error with collection operations:', error.message);
    }
}

async function main() {
    try {
        console.log('🎯 Shopify Variants and Collections Demo');
        console.log('========================================\n');
        
        const shop = await shopify.shop.get();
        console.log(`Store: ${shop.name} (${shop.myshopify_domain})\n`);
        
        // Step 1: Add variants to existing products
        await addVariantsToExistingProducts();
        
        // Step 2: Add images to products
        await addImagesToProducts();
        
        // Step 3: Demonstrate collection sorting
        await demonstrateCollectionSorting();
        
        console.log('\n🎉 DEMO COMPLETED!');
        console.log('==================');
        console.log('✅ Added variants to existing products');
        console.log('✅ Updated vendors and product types');
        console.log('✅ Added thumbnail images');
        console.log('✅ Updated collection sorting to A-Z');
        console.log('✅ Created collection with lowest prices first');
        
        console.log('\n📝 Summary of what was accomplished:');
        console.log('• Added 2 variants to each of the first 5 products');
        console.log('• Set different inventory levels (some out of stock)');
        console.log('• Updated vendors and product types');
        console.log('• Added product images');
        console.log('• Sorted collections by A-Z order');
        console.log('• Created a collection sorted by lowest prices first');
        
        console.log('\n💡 For catalog page sorting:');
        console.log('• The "Lowest Prices First" collection demonstrates price sorting');
        console.log('• Use Shopify Admin to set default collection sorting');
        console.log('• Or modify theme templates with Liquid sorting filters');
        
    } catch (error) {
        console.error('❌ Fatal error:', error.message);
    }
}

main().catch(console.error);
